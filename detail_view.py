# coding:utf-8
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QScrollArea, QPushButton, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from qfluentwidgets import (CardWidget, SubtitleLabel, BodyLabel, PrimaryPushButton,
                           PushButton, FluentIcon as FIF, ToolButton)
from rich_text_display import CompactRichTextDisplay
from models import TodoItem, IssueItem
from datetime import datetime

class DetailView(QWidget):
    """详情查看页面基类"""
    
    back_requested = pyqtSignal()  # 返回信号
    edit_requested = pyqtSignal(str)  # 编辑信号，传递item_id
    delete_requested = pyqtSignal(str)  # 删除信号，传递item_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_item = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        
        # 返回按钮
        self.back_btn = ToolButton(FIF.LEFT_ARROW)
        self.back_btn.setToolTip("返回列表")
        self.back_btn.clicked.connect(self.back_requested.emit)
        toolbar_layout.addWidget(self.back_btn)
        
        # 标题区域
        self.title_label = SubtitleLabel("详情")
        self.title_label.setStyleSheet("margin: 0px; padding: 0px;")
        toolbar_layout.addWidget(self.title_label)
        
        # 弹性空间
        toolbar_layout.addStretch()
        
        # 编辑按钮
        self.edit_btn = PrimaryPushButton("编辑")
        self.edit_btn.setIcon(FIF.EDIT)
        self.edit_btn.clicked.connect(self._on_edit_clicked)
        toolbar_layout.addWidget(self.edit_btn)
        
        # 删除按钮
        self.delete_btn = PushButton("删除")
        self.delete_btn.setIcon(FIF.DELETE)
        self.delete_btn.clicked.connect(self._on_delete_clicked)
        toolbar_layout.addWidget(self.delete_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)
        
        # 内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(15)
        
        scroll_area.setWidget(self.content_widget)
        layout.addWidget(scroll_area)
        
    def _on_edit_clicked(self):
        """编辑按钮点击"""
        if self.current_item:
            self.edit_requested.emit(self.current_item.id)
            
    def _on_delete_clicked(self):
        """删除按钮点击"""
        if self.current_item:
            self.delete_requested.emit(self.current_item.id)
            
    def set_item(self, item):
        """设置要显示的项目"""
        self.current_item = item
        self._update_content()
        
    def _update_content(self):
        """更新内容显示"""
        # 清除现有内容
        for i in reversed(range(self.content_layout.count())):
            child = self.content_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        if not self.current_item:
            return
            
        # 添加基本信息
        self._add_basic_info()
        
        # 添加描述内容
        self._add_description()
        
        # 添加元数据
        self._add_metadata()
        
    def _add_basic_info(self):
        """添加基本信息"""
        # 标题卡片
        title_card = CardWidget()
        title_layout = QVBoxLayout(title_card)
        title_layout.setContentsMargins(20, 15, 20, 15)
        
        # 类型标签
        type_label = BodyLabel(self._get_type_text())
        type_label.setStyleSheet("color: #666; font-size: 12px;")
        title_layout.addWidget(type_label)
        
        # 标题
        title_label = SubtitleLabel(self.current_item.title)
        title_label.setWordWrap(True)
        title_layout.addWidget(title_label)
        
        self.content_layout.addWidget(title_card)
        
    def _add_description(self):
        """添加描述内容"""
        if not self.current_item.description:
            return
            
        # 描述卡片
        desc_card = CardWidget()
        desc_layout = QVBoxLayout(desc_card)
        desc_layout.setContentsMargins(20, 15, 20, 15)
        
        # 描述标题
        desc_title = BodyLabel("描述")
        desc_title.setStyleSheet("font-weight: bold; margin: 0px; padding: 0px;")
        desc_layout.addWidget(desc_title)
        
        # 描述内容 - 使用富文本显示，显示完整内容包括图片
        desc_display = CompactRichTextDisplay()
        desc_display.setRichText(self.current_item.description, max_height=600, show_images=True)
        desc_display.setStyleSheet("border: none; background: transparent;")
        desc_layout.addWidget(desc_display)
        
        self.content_layout.addWidget(desc_card)
        
    def _add_metadata(self):
        """添加元数据"""
        # 元数据卡片
        meta_card = CardWidget()
        meta_layout = QVBoxLayout(meta_card)
        meta_layout.setContentsMargins(20, 15, 20, 15)
        
        # 元数据标题
        meta_title = BodyLabel("信息")
        meta_title.setStyleSheet("font-weight: bold; margin: 0px; padding: 0px;")
        meta_layout.addWidget(meta_title)
        
        # 创建时间
        created_time = self.current_item.created_at.strftime("%Y-%m-%d %H:%M:%S")
        created_label = BodyLabel(f"创建时间: {created_time}")
        created_label.setStyleSheet("color: #666; font-size: 12px;")
        meta_layout.addWidget(created_label)
        
        # ID信息
        id_label = BodyLabel(f"ID: {self.current_item.id}")
        id_label.setStyleSheet("color: #666; font-size: 12px;")
        meta_layout.addWidget(id_label)
        
        # 添加特定类型的元数据
        self._add_specific_metadata(meta_layout)
        
        self.content_layout.addWidget(meta_card)
        
    def _get_type_text(self) -> str:
        """获取类型文本"""
        return "项目"
        
    def _add_specific_metadata(self, layout):
        """添加特定类型的元数据"""
        pass

class TodoDetailView(DetailView):
    """TODO详情页面"""
    
    def _get_type_text(self) -> str:
        return "TODO"
        
    def _add_specific_metadata(self, layout):
        """添加TODO特定的元数据"""
        if isinstance(self.current_item, TodoItem):
            # 完成状态
            status_text = "已完成" if self.current_item.completed else "未完成"
            status_color = "#4CAF50" if self.current_item.completed else "#FF9800"
            status_label = BodyLabel(f"状态: {status_text}")
            status_label.setStyleSheet(f"color: {status_color}; font-size: 12px; font-weight: bold;")
            layout.addWidget(status_label)
            
            # 排序
            order_label = BodyLabel(f"排序: {self.current_item.order}")
            order_label.setStyleSheet("color: #666; font-size: 12px;")
            layout.addWidget(order_label)

class IssueDetailView(DetailView):
    """Issue详情页面"""
    
    def _get_type_text(self) -> str:
        return "Issue"
        
    def _add_specific_metadata(self, layout):
        """添加Issue特定的元数据"""
        if isinstance(self.current_item, IssueItem):
            # 排序
            order_label = BodyLabel(f"排序: {self.current_item.order}")
            order_label.setStyleSheet("color: #666; font-size: 12px;")
            layout.addWidget(order_label)
