# coding:utf-8
"""
最终改进功能测试
验证所有新功能：图片粘贴、页面编辑、详情查看、样式优化等
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel, QTextEdit
from PyQt6.QtCore import Qt
from edit_view import TodoEditView, IssueEditView
from detail_view import TodoDetailView, IssueDetailView
from models import Project, TodoItem, IssueItem
import base64

class FinalImprovementsTestWindow(QMainWindow):
    """最终改进功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最终改进功能测试")
        self.setGeometry(100, 100, 1200, 800)
        self.setup_ui()
        self.create_test_data()
        
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 说明文字
        instruction = QLabel("""
        🎉 最终改进功能测试

        本测试验证以下改进：
        1. ✅ 图片粘贴支持 (Ctrl+V)
        2. ✅ 列表显示优化 (图片显示为[图片]标记)
        3. ✅ 页面编辑模式 (替代对话框编辑)
        4. ✅ 详情页面查看 (点击列表项查看完整内容)
        5. ✅ 样式优化 (减少边距)
        """)
        instruction.setStyleSheet("""
            background: #f0f8ff; 
            padding: 15px; 
            border-radius: 8px; 
            border: 1px solid #e0e0e0;
            font-size: 14px;
        """)
        layout.addWidget(instruction)
        
        # 测试按钮区域
        button_layout = QHBoxLayout()
        
        # 页面编辑测试
        button_layout.addWidget(QPushButton("测试TODO编辑页面", clicked=self.test_todo_edit_page))
        button_layout.addWidget(QPushButton("测试Issue编辑页面", clicked=self.test_issue_edit_page))
        
        # 详情页面测试
        button_layout.addWidget(QPushButton("测试TODO详情页面", clicked=self.test_todo_detail_page))
        button_layout.addWidget(QPushButton("测试Issue详情页面", clicked=self.test_issue_detail_page))
        
        layout.addLayout(button_layout)
        
        # 结果显示区域
        self.result_display = QTextEdit()
        self.result_display.setMaximumHeight(200)
        layout.addWidget(QLabel("测试结果:"))
        layout.addWidget(self.result_display)
        
        # 功能说明
        features_text = """
        📋 功能说明：

        🖼️ 图片粘贴：在编辑页面的富文本编辑器中，可以直接按Ctrl+V粘贴剪贴板中的图片

        📝 页面编辑：编辑功能从小对话框改为全页面编辑，提供更大的编辑空间和更好的用户体验

        👁️ 智能显示：列表中图片显示为[图片]标记，避免影响列表性能；详情页面显示完整图片

        🎨 样式优化：减少了详情页面中标题的过大边距，界面更加紧凑美观

        🔄 导航优化：支持列表→详情→编辑之间的流畅切换，提供完整的用户操作流程
        """
        
        features_label = QLabel(features_text)
        features_label.setStyleSheet("""
            background: #f9f9f9; 
            padding: 15px; 
            border-radius: 8px; 
            border: 1px solid #e0e0e0;
            font-size: 12px;
        """)
        features_label.setWordWrap(True)
        layout.addWidget(features_label)
        
    def create_test_data(self):
        """创建测试数据"""
        self.test_project = Project(name="最终测试项目", description="验证所有改进功能")
        
        # 创建包含图片的HTML内容
        test_image_html = self._create_test_html_with_image()
        
        # 添加测试TODO项
        self.test_todo = self.test_project.add_todo(
            "包含图片的TODO项目",
            test_image_html
        )
        
        # 添加测试Issue项
        self.test_issue = self.test_project.add_issue(
            "包含图片的Issue项目",
            test_image_html
        )
        
    def test_todo_edit_page(self):
        """测试TODO编辑页面"""
        try:
            # 创建TODO编辑页面
            edit_window = TodoEditView()
            edit_window.setWindowTitle("TODO编辑页面测试")
            edit_window.setGeometry(200, 200, 1000, 700)
            
            # 设置测试数据（编辑模式）
            edit_window.set_item(self.test_todo, is_new=False)
            
            # 连接信号
            edit_window.save_requested.connect(
                lambda item_id, title, desc: self.result_display.append(
                    f"✅ TODO保存: ID={item_id[:8]}..., 标题={title}, 描述长度={len(desc)}"
                )
            )
            edit_window.back_requested.connect(
                lambda: self.result_display.append("✅ TODO编辑页面返回")
            )
            
            edit_window.show()
            self.todo_edit_window = edit_window  # 保持引用
            
            self.result_display.append("✅ TODO编辑页面已打开 - 请测试图片粘贴功能")
            
        except Exception as e:
            self.result_display.append(f"❌ TODO编辑页面测试失败: {str(e)}")
            
    def test_issue_edit_page(self):
        """测试Issue编辑页面"""
        try:
            # 创建Issue编辑页面
            edit_window = IssueEditView()
            edit_window.setWindowTitle("Issue编辑页面测试")
            edit_window.setGeometry(250, 250, 1000, 700)
            
            # 设置测试数据（新建模式）
            from models import IssueItem
            new_issue = IssueItem(title="", description="")
            edit_window.set_item(new_issue, is_new=True)
            
            # 连接信号
            edit_window.save_requested.connect(
                lambda item_id, title, desc: self.result_display.append(
                    f"✅ Issue保存: 标题={title}, 描述长度={len(desc)}"
                )
            )
            edit_window.back_requested.connect(
                lambda: self.result_display.append("✅ Issue编辑页面返回")
            )
            
            edit_window.show()
            self.issue_edit_window = edit_window  # 保持引用
            
            self.result_display.append("✅ Issue编辑页面已打开 - 请测试新建功能")
            
        except Exception as e:
            self.result_display.append(f"❌ Issue编辑页面测试失败: {str(e)}")
            
    def test_todo_detail_page(self):
        """测试TODO详情页面"""
        try:
            # 创建TODO详情页面
            detail_window = TodoDetailView()
            detail_window.setWindowTitle("TODO详情页面测试")
            detail_window.setGeometry(300, 300, 800, 600)
            
            # 设置测试数据
            detail_window.set_item(self.test_todo)
            
            # 连接信号
            detail_window.edit_requested.connect(
                lambda item_id: self.result_display.append(f"✅ 请求编辑TODO: {item_id[:8]}...")
            )
            detail_window.delete_requested.connect(
                lambda item_id: self.result_display.append(f"✅ 请求删除TODO: {item_id[:8]}...")
            )
            detail_window.back_requested.connect(
                lambda: self.result_display.append("✅ TODO详情页面返回")
            )
            
            detail_window.show()
            self.todo_detail_window = detail_window  # 保持引用
            
            self.result_display.append("✅ TODO详情页面已打开 - 可以看到完整图片")
            
        except Exception as e:
            self.result_display.append(f"❌ TODO详情页面测试失败: {str(e)}")
            
    def test_issue_detail_page(self):
        """测试Issue详情页面"""
        try:
            # 创建Issue详情页面
            detail_window = IssueDetailView()
            detail_window.setWindowTitle("Issue详情页面测试")
            detail_window.setGeometry(350, 350, 800, 600)
            
            # 设置测试数据
            detail_window.set_item(self.test_issue)
            
            # 连接信号
            detail_window.edit_requested.connect(
                lambda item_id: self.result_display.append(f"✅ 请求编辑Issue: {item_id[:8]}...")
            )
            detail_window.delete_requested.connect(
                lambda item_id: self.result_display.append(f"✅ 请求删除Issue: {item_id[:8]}...")
            )
            detail_window.back_requested.connect(
                lambda: self.result_display.append("✅ Issue详情页面返回")
            )
            
            detail_window.show()
            self.issue_detail_window = detail_window  # 保持引用
            
            self.result_display.append("✅ Issue详情页面已打开 - 注意样式优化")
            
        except Exception as e:
            self.result_display.append(f"❌ Issue详情页面测试失败: {str(e)}")
            
    def _create_test_html_with_image(self) -> str:
        """创建包含图片的测试HTML"""
        try:
            from PyQt6.QtGui import QPixmap, QPainter
            from PyQt6.QtCore import QBuffer, QIODevice
            
            # 创建测试图片
            pixmap = QPixmap(150, 100)
            pixmap.fill(Qt.GlobalColor.lightGreen)
            
            painter = QPainter(pixmap)
            painter.setPen(Qt.GlobalColor.darkGreen)
            painter.drawText(30, 50, "测试图片")
            painter.drawRect(5, 5, 140, 90)
            painter.end()
            
            # 转换为Base64
            buffer = QBuffer()
            buffer.open(QIODevice.OpenModeFlag.WriteOnly)
            pixmap.save(buffer, "PNG")
            image_data = buffer.data().data()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            data_url = f"data:image/png;base64,{base64_data}"
            
            html = f'''
            <p><b>这是一个包含图片的测试内容</b></p>
            <p>功能特点：</p>
            <ul>
                <li><b>图片粘贴</b>：支持Ctrl+V直接粘贴</li>
                <li><i>智能显示</i>：列表中显示[图片]标记</li>
                <li><u>完整查看</u>：详情页面显示完整图片</li>
            </ul>
            <p>下面是测试图片：</p>
            <img src="{data_url}" style="max-width: 150px; max-height: 100px;" />
            <p style="color: blue;">这是蓝色的说明文字</p>
            <p>这段文字用于测试文本截断和工具提示功能。在列表中会被截断，完整内容显示在工具提示中。</p>
            '''
            return html
            
        except Exception as e:
            return f'<p><b>创建测试HTML失败: {str(e)}</b></p>'

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = FinalImprovementsTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
