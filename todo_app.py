# coding:utf-8
import os
from PyQt6.QtWidgets import Q<PERSON>ainWindow, QStackedWidget
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon
from qfluentwidgets import setTheme, Theme, FluentIcon as FIF
from data_manager import DataManager
from project_list_widget import ProjectListWidget
from project_detail_widget import ProjectDetailWidget

class TodoApp(QMainWindow):
    """TODO应用主窗口"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = DataManager()
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("TODO项目管理器")
        self.setMinimumSize(1000, 700)

        # 设置窗口图标
        self.set_window_icon()

        # 设置主题
        setTheme(Theme.LIGHT)
        
        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # 创建页面
        self.project_list_widget = ProjectListWidget()
        self.project_detail_widget = ProjectDetailWidget()
        
        # 添加页面到堆叠窗口
        self.stacked_widget.addWidget(self.project_list_widget)
        self.stacked_widget.addWidget(self.project_detail_widget)
        
        # 初始显示项目列表
        self.show_project_list()

    def set_window_icon(self):
        """设置窗口图标"""
        icon_path = "resources/logo.ico"

        # 检查图标文件是否存在
        if os.path.exists(icon_path):
            try:
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
                print(f"✅ 成功设置窗口图标: {icon_path}")
            except Exception as e:
                print(f"❌ 设置窗口图标失败: {e}")
        else:
            print(f"⚠️ 图标文件不存在: {icon_path}")
            print("   请确保图标文件位于正确的路径")

    def setup_connections(self):
        """设置信号连接"""
        # 项目列表页面信号
        self.project_list_widget.project_selected.connect(self.show_project_detail)
        
        # 项目详情页面信号
        self.project_detail_widget.back_requested.connect(self.show_project_list)
        self.project_detail_widget.project_updated.connect(self.on_project_updated)
        
    def show_project_list(self):
        """显示项目列表页面"""
        projects = self.data_manager.get_projects()
        self.project_list_widget.set_projects(projects)
        self.stacked_widget.setCurrentWidget(self.project_list_widget)
        
    def show_project_detail(self, project_id: str):
        """显示项目详情页面"""
        project = self.data_manager.get_project(project_id)
        if project:
            self.project_detail_widget.set_project(project)
            self.stacked_widget.setCurrentWidget(self.project_detail_widget)
            
    def add_new_project(self, name: str, description: str):
        """添加新项目"""
        project = self.data_manager.add_project(name, description)
        self.show_project_list()  # 刷新项目列表

    def delete_project(self, project_id: str):
        """删除项目"""
        success = self.data_manager.remove_project(project_id)
        if success:
            # 如果当前正在查看被删除的项目，返回到项目列表
            if (hasattr(self.project_detail_widget, 'current_project') and
                self.project_detail_widget.current_project and
                self.project_detail_widget.current_project.id == project_id):
                self.show_project_list()
            else:
                # 否则只刷新项目列表
                self.show_project_list()

    def on_project_updated(self, project):
        """项目更新时的处理"""
        self.data_manager.update_project(project)


def main():
    """主函数"""
    import sys
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # 创建主窗口
    window = TodoApp()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
