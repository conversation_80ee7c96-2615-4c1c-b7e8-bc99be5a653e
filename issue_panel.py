# coding:utf-8
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QScrollArea, QListWidget, QListWidgetItem)
from PyQt6.QtCore import Qt, pyqtSignal
from qfluentwidgets import (PrimaryPushButton, PushButton, CardWidget, BodyLabel,
                           SubtitleLabel, FluentIcon as FIF, MessageBox, LineEdit,
                           TextEdit, ToolButton)
from rich_text_editor import RichTextEditor
from rich_text_display import RichTextDisplay
from models import IssueItem
from text_utils import get_display_text_and_tooltip
from typing import List

class IssueItemWidget(CardWidget):
    """Issue项组件"""
    edit_requested = pyqtSignal(str)  # issue_id
    delete_requested = pyqtSignal(str)  # issue_id
    
    def __init__(self, issue_item: IssueItem, parent=None):
        super().__init__(parent)
        self.issue_item = issue_item
        self.setFixedHeight(80)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        
        # 内容区域
        content_layout = QVBoxLayout()
        
        # 标题
        title_display, title_tooltip = get_display_text_and_tooltip(self.issue_item.title, 25)
        self.title_label = SubtitleLabel(title_display)
        if title_tooltip:
            self.title_label.setToolTip(title_tooltip)

        # 描述 - 使用富文本显示
        self.desc_label = RichTextDisplay()
        self.desc_label.setRichText(self.issue_item.description, 40)
        
        content_layout.addWidget(self.title_label)
        if self.issue_item.description:
            content_layout.addWidget(self.desc_label)
        
        # 操作按钮
        button_layout = QVBoxLayout()
        
        self.edit_button = ToolButton(FIF.EDIT)
        self.edit_button.setFixedSize(32, 32)
        self.edit_button.clicked.connect(lambda: self.edit_requested.emit(self.issue_item.id))
        
        self.delete_button = ToolButton(FIF.DELETE)
        self.delete_button.setFixedSize(32, 32)
        self.delete_button.clicked.connect(lambda: self.delete_requested.emit(self.issue_item.id))
        
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        
        layout.addLayout(content_layout, 1)
        layout.addLayout(button_layout)
    
    def update_issue(self, issue_item: IssueItem):
        """更新Issue项"""
        self.issue_item = issue_item

        # 更新标题
        title_display, title_tooltip = get_display_text_and_tooltip(issue_item.title, 25)
        self.title_label.setText(title_display)
        self.title_label.setToolTip(title_tooltip if title_tooltip else "")

        # 更新描述 - 使用富文本显示
        self.desc_label.setRichText(issue_item.description, 40)

class DraggableIssueListWidget(QListWidget):
    """支持拖拽的Issue列表组件"""
    items_reordered = pyqtSignal(list)  # 发送重新排序后的item_ids
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.DragDropMode.InternalMove)
        self.setDefaultDropAction(Qt.DropAction.MoveAction)
        
    def dropEvent(self, event):
        """拖拽放置事件"""
        super().dropEvent(event)
        # 获取重新排序后的item_ids
        item_ids = []
        for i in range(self.count()):
            item = self.item(i)
            if item and hasattr(item, 'issue_id'):
                item_ids.append(item.issue_id)
        self.items_reordered.emit(item_ids)

class IssueEditDialog(MessageBox):
    """Issue编辑对话框"""
    
    def __init__(self, issue_item: IssueItem = None, parent=None):
        title = "编辑Issue" if issue_item else "添加Issue"
        super().__init__(title, "请输入Issue信息", parent)
        self.issue_item = issue_item
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        # 标题输入
        self.title_edit = LineEdit()
        self.title_edit.setPlaceholderText("Issue标题")
        if self.issue_item:
            self.title_edit.setText(self.issue_item.title)
        
        # 描述输入 - 使用富文本编辑器
        self.desc_edit = RichTextEditor()
        self.desc_edit.setPlaceholderText("Issue描述（可选）")
        self.desc_edit.setFixedHeight(120)  # 增加高度以容纳工具栏
        if self.issue_item:
            # 检查是否为HTML格式
            if self.issue_item.description.strip().startswith('<') and self.issue_item.description.strip().endswith('>'):
                self.desc_edit.setHtml(self.issue_item.description)
            else:
                self.desc_edit.setPlainText(self.issue_item.description)
        
        # 添加到对话框
        self.textLayout.addWidget(QLabel("标题:"))
        self.textLayout.addWidget(self.title_edit)
        self.textLayout.addWidget(QLabel("描述:"))
        self.textLayout.addWidget(self.desc_edit)
        
    def get_issue_info(self):
        """获取Issue信息"""
        title = self.title_edit.text().strip()
        # 获取富文本内容，如果有格式化则保存为HTML，否则保存为纯文本
        html_content = self.desc_edit.toHtml()
        plain_content = self.desc_edit.toPlainText().strip()

        # 检查是否有富文本格式
        if self._has_rich_formatting(html_content):
            description = html_content
        else:
            description = plain_content

        return title, description

    def _has_rich_formatting(self, html: str) -> bool:
        """检查HTML内容是否包含富文本格式"""
        # 简单检查是否包含格式化标签
        formatting_tags = ['<b>', '<i>', '<u>', '<font', '<span', '<p style=']
        return any(tag in html for tag in formatting_tags)

class IssuePanel(QWidget):
    """Issue面板"""
    issue_updated = pyqtSignal()  # Issue更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.issues: List[IssueItem] = []
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        
        # 标题和添加按钮
        header_layout = QHBoxLayout()
        
        self.title_label = SubtitleLabel("Issue列表")
        self.add_button = PrimaryPushButton(FIF.ADD, "添加Issue")
        self.add_button.clicked.connect(self.add_issue)
        
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.add_button)
        
        # Issue列表
        self.list_widget = DraggableIssueListWidget()
        self.list_widget.items_reordered.connect(self.reorder_issues)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.list_widget)
        
    def set_issues(self, issues: List[IssueItem]):
        """设置Issue列表"""
        self.issues = sorted(issues, key=lambda x: x.order)
        self.refresh_issues()
        
    def refresh_issues(self):
        """刷新Issue列表显示"""
        self.list_widget.clear()
        
        for issue in self.issues:
            item = QListWidgetItem()
            item.issue_id = issue.id
            item.setSizeHint(IssueItemWidget(issue).sizeHint())
            
            widget = IssueItemWidget(issue)
            widget.edit_requested.connect(self.edit_issue)
            widget.delete_requested.connect(self.delete_issue)
            
            self.list_widget.addItem(item)
            self.list_widget.setItemWidget(item, widget)
            
    def add_issue(self):
        """添加Issue"""
        dialog = IssueEditDialog(parent=self)
        if dialog.exec():
            title, description = dialog.get_issue_info()
            if title:
                # 通知父组件添加Issue
                parent = self.parent()
                while parent and not hasattr(parent, 'add_issue_item'):
                    parent = parent.parent()
                if parent:
                    parent.add_issue_item(title, description)
            else:
                MessageBox("错误", "Issue标题不能为空", self).exec()

    def edit_issue(self, issue_id: str):
        """编辑Issue"""
        issue = next((i for i in self.issues if i.id == issue_id), None)
        if issue:
            dialog = IssueEditDialog(issue, self)
            if dialog.exec():
                title, description = dialog.get_issue_info()
                if title:
                    # 通知父组件更新Issue
                    parent = self.parent()
                    while parent and not hasattr(parent, 'update_issue_item'):
                        parent = parent.parent()
                    if parent:
                        parent.update_issue_item(issue_id, title, description)
                else:
                    MessageBox("错误", "Issue标题不能为空", self).exec()

    def delete_issue(self, issue_id: str):
        """删除Issue"""
        reply = MessageBox("确认删除", "确定要删除这个Issue吗？", self)
        if reply.exec():
            # 通知父组件删除Issue
            parent = self.parent()
            while parent and not hasattr(parent, 'delete_issue_item'):
                parent = parent.parent()
            if parent:
                parent.delete_issue_item(issue_id)

    def reorder_issues(self, issue_ids: List[str]):
        """重新排序Issue"""
        # 通知父组件重新排序Issue
        parent = self.parent()
        while parent and not hasattr(parent, 'reorder_issue_items'):
            parent = parent.parent()
        if parent:
            parent.reorder_issue_items(issue_ids)
