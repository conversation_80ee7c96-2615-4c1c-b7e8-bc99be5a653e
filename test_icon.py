# coding:utf-8
"""
测试窗口图标功能
"""

import os
import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
from PyQt6.QtGui import QIcon
from PyQt6.QtCore import Qt

class IconTestWindow(QMainWindow):
    """图标测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("图标测试窗口")
        self.setGeometry(100, 100, 400, 300)
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("图标测试窗口")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        
        status_label = QLabel()
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setWordWrap(True)
        
        # 检查图标状态
        icon_path = "resources/logo.ico"
        if os.path.exists(icon_path):
            status_text = f"✅ 图标文件存在: {icon_path}\n"
            status_text += "如果窗口标题栏显示了自定义图标，说明设置成功！"
            status_label.setStyleSheet("color: green;")
        else:
            status_text = f"❌ 图标文件不存在: {icon_path}\n"
            status_text += "请确保图标文件位于正确的路径"
            status_label.setStyleSheet("color: red;")
        
        status_label.setText(status_text)
        
        layout.addWidget(label)
        layout.addWidget(status_label)
        
    def set_window_icon(self):
        """设置窗口图标"""
        icon_path = "resources/logo.ico"
        
        print(f"正在尝试设置图标: {icon_path}")
        
        # 检查图标文件是否存在
        if os.path.exists(icon_path):
            try:
                icon = QIcon(icon_path)
                
                # 检查图标是否为空
                if icon.isNull():
                    print(f"❌ 图标文件无效或损坏: {icon_path}")
                else:
                    self.setWindowIcon(icon)
                    print(f"✅ 成功设置窗口图标: {icon_path}")
                    
                    # 显示图标信息
                    available_sizes = icon.availableSizes()
                    if available_sizes:
                        print(f"   可用尺寸: {[f'{size.width()}x{size.height()}' for size in available_sizes]}")
                    else:
                        print("   图标尺寸信息不可用")
                        
            except Exception as e:
                print(f"❌ 设置窗口图标失败: {e}")
        else:
            print(f"⚠️ 图标文件不存在: {icon_path}")
            print("   请确保图标文件位于正确的路径")
            
            # 尝试查找其他可能的图标文件
            possible_paths = [
                "logo.ico",
                "icon.ico",
                "resources/icon.ico",
                "assets/logo.ico",
                "images/logo.ico"
            ]
            
            print("   正在搜索其他可能的图标文件...")
            for path in possible_paths:
                if os.path.exists(path):
                    print(f"   找到图标文件: {path}")
                    break
            else:
                print("   未找到任何图标文件")

def test_icon_functionality():
    """测试图标功能"""
    print("🎯 图标功能测试")
    print("=" * 50)
    
    # 检查图标文件
    icon_path = "resources/logo.ico"
    print(f"检查图标文件: {icon_path}")
    
    if os.path.exists(icon_path):
        file_size = os.path.getsize(icon_path)
        print(f"✅ 文件存在，大小: {file_size} 字节")
        
        # 尝试创建QIcon对象
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            icon = QIcon(icon_path)
            if icon.isNull():
                print("❌ 图标文件无效或损坏")
            else:
                print("✅ 图标文件有效")
                available_sizes = icon.availableSizes()
                if available_sizes:
                    print(f"   可用尺寸: {[f'{size.width()}x{size.height()}' for size in available_sizes]}")
                
        except Exception as e:
            print(f"❌ 创建图标对象失败: {e}")
    else:
        print(f"❌ 文件不存在")
        
        # 检查当前目录结构
        print("\n当前目录结构:")
        for root, dirs, files in os.walk("."):
            level = root.replace(".", "").count(os.sep)
            indent = " " * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = " " * 2 * (level + 1)
            for file in files:
                if file.endswith(('.ico', '.png', '.jpg', '.jpeg')):
                    print(f"{subindent}{file}")

if __name__ == "__main__":
    # 先进行基本测试
    test_icon_functionality()
    
    print("\n" + "=" * 50)
    print("启动图标测试窗口...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = IconTestWindow()
    window.show()
    
    print("图标测试窗口已启动，请检查窗口标题栏是否显示了自定义图标")
    print("关闭窗口以结束测试")
    
    # 运行应用程序
    sys.exit(app.exec())
