# coding:utf-8
"""
图片支持功能测试
验证富文本编辑器的图片插入、显示和保存功能
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QPushButton, QHBoxLayout, QLabel, QTextEdit)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap, QPainter
from rich_text_editor import RichTextEditor
from rich_text_display import RichTextDisplay, CompactRichTextDisplay
from models import Project, TodoItem, IssueItem
from data_manager import DataManager
import base64

class ImageTestWindow(QMainWindow):
    """图片功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图片支持功能测试")
        self.setGeometry(100, 100, 1000, 700)
        self.setup_ui()
        self.create_test_image()
        
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮区域
        button_layout = QHBoxLayout()
        
        # 富文本编辑器测试
        button_layout.addWidget(QPushButton("测试富文本编辑器", clicked=self.test_rich_text_editor))
        
        # 图片显示测试
        button_layout.addWidget(QPushButton("测试图片显示", clicked=self.test_image_display))
        
        # 数据保存测试
        button_layout.addWidget(QPushButton("测试数据保存", clicked=self.test_data_persistence))
        
        # 创建测试图片
        button_layout.addWidget(QPushButton("创建测试图片", clicked=self.create_test_image))
        
        layout.addLayout(button_layout)
        
        # 结果显示区域
        self.result_display = QTextEdit()
        self.result_display.setMaximumHeight(200)
        layout.addWidget(QLabel("测试结果:"))
        layout.addWidget(self.result_display)
        
        # 预览区域
        self.preview_area = RichTextDisplay()
        layout.addWidget(QLabel("预览区域:"))
        layout.addWidget(self.preview_area)
        
    def create_test_image(self):
        """创建测试图片"""
        try:
            # 创建一个简单的测试图片
            pixmap = QPixmap(200, 150)
            pixmap.fill(Qt.GlobalColor.lightGray)
            
            painter = QPainter(pixmap)
            painter.setPen(Qt.GlobalColor.blue)
            painter.drawText(50, 75, "测试图片")
            painter.drawRect(10, 10, 180, 130)
            painter.end()
            
            # 保存测试图片
            test_image_path = "test_image.png"
            pixmap.save(test_image_path, "PNG")
            
            self.result_display.append(f"✅ 测试图片已创建: {test_image_path}")
            
        except Exception as e:
            self.result_display.append(f"❌ 创建测试图片失败: {str(e)}")
            
    def test_rich_text_editor(self):
        """测试富文本编辑器的图片功能"""
        try:
            # 创建编辑器窗口
            editor_window = QWidget()
            editor_window.setWindowTitle("富文本编辑器 - 图片测试")
            editor_window.setGeometry(200, 200, 800, 600)
            
            layout = QVBoxLayout(editor_window)
            
            # 富文本编辑器
            editor = RichTextEditor()
            editor.setPlaceholderText("请点击工具栏中的图片按钮插入图片...")
            layout.addWidget(editor)
            
            # 测试按钮
            button_layout = QHBoxLayout()
            
            def get_html_content():
                html = editor.toHtml()
                self.result_display.append("HTML内容:")
                self.result_display.append(html[:500] + "..." if len(html) > 500 else html)
                
                # 在预览区域显示
                self.preview_area.setRichText(html)
                
            def set_test_content():
                # 设置包含图片的测试内容
                test_html = self._create_test_html_with_image()
                editor.setHtml(test_html)
                
            def clear_content():
                editor.clear()
                
            button_layout.addWidget(QPushButton("获取HTML内容", clicked=get_html_content))
            button_layout.addWidget(QPushButton("设置测试内容", clicked=set_test_content))
            button_layout.addWidget(QPushButton("清空内容", clicked=clear_content))
            
            layout.addLayout(button_layout)
            
            editor_window.show()
            self.editor_window = editor_window  # 保持引用
            
            self.result_display.append("✅ 富文本编辑器测试窗口已打开")
            
        except Exception as e:
            self.result_display.append(f"❌ 富文本编辑器测试失败: {str(e)}")
            
    def test_image_display(self):
        """测试图片显示功能"""
        try:
            # 创建显示测试窗口
            display_window = QWidget()
            display_window.setWindowTitle("图片显示测试")
            display_window.setGeometry(300, 300, 600, 500)
            
            layout = QVBoxLayout(display_window)
            
            # 测试不同的显示组件
            test_html = self._create_test_html_with_image()
            
            # RichTextDisplay测试
            layout.addWidget(QLabel("RichTextDisplay:"))
            rich_display = RichTextDisplay()
            rich_display.setRichText(test_html, 100)
            layout.addWidget(rich_display)
            
            # CompactRichTextDisplay测试
            layout.addWidget(QLabel("CompactRichTextDisplay:"))
            compact_display = CompactRichTextDisplay()
            compact_display.setRichText(test_html, 150)
            layout.addWidget(compact_display)
            
            # 原始HTML显示
            layout.addWidget(QLabel("原始HTML:"))
            html_display = QTextEdit()
            html_display.setPlainText(test_html)
            html_display.setMaximumHeight(100)
            layout.addWidget(html_display)
            
            display_window.show()
            self.display_window = display_window  # 保持引用
            
            self.result_display.append("✅ 图片显示测试窗口已打开")
            
        except Exception as e:
            self.result_display.append(f"❌ 图片显示测试失败: {str(e)}")
            
    def test_data_persistence(self):
        """测试包含图片的数据保存和加载"""
        try:
            # 创建包含图片的测试项目
            project = Project(name="图片测试项目", description="用于测试图片功能")
            
            # 添加包含图片的TODO
            image_html = self._create_test_html_with_image()
            todo = project.add_todo("包含图片的TODO", image_html)
            
            # 添加包含图片的Issue
            issue = project.add_issue("包含图片的Issue", image_html)
            
            # 保存数据
            data_manager = DataManager("test_image_data.json")
            data_manager.save_projects([project])
            
            self.result_display.append("✅ 包含图片的数据已保存")
            
            # 加载数据
            loaded_projects = data_manager.load_projects()
            
            if loaded_projects:
                loaded_project = loaded_projects[0]
                self.result_display.append(f"✅ 数据加载成功: {loaded_project.name}")
                
                # 检查TODO
                if loaded_project.todos:
                    todo_desc = loaded_project.todos[0].description
                    has_image = '<img' in todo_desc
                    self.result_display.append(f"TODO包含图片: {'是' if has_image else '否'}")
                    
                # 检查Issue
                if loaded_project.issues:
                    issue_desc = loaded_project.issues[0].description
                    has_image = '<img' in issue_desc
                    self.result_display.append(f"Issue包含图片: {'是' if has_image else '否'}")
                    
                # 在预览区域显示加载的内容
                if loaded_project.todos:
                    self.preview_area.setRichText(loaded_project.todos[0].description)
                    
            else:
                self.result_display.append("❌ 没有加载到数据")
                
        except Exception as e:
            self.result_display.append(f"❌ 数据持久化测试失败: {str(e)}")
            
    def _create_test_html_with_image(self) -> str:
        """创建包含图片的测试HTML"""
        try:
            # 读取测试图片并转换为Base64
            test_image_path = "test_image.png"
            if os.path.exists(test_image_path):
                with open(test_image_path, 'rb') as f:
                    image_data = f.read()
                    base64_data = base64.b64encode(image_data).decode('utf-8')
                    data_url = f"data:image/png;base64,{base64_data}"
                    
                    html = f'''
                    <p><b>这是一个包含图片的测试内容</b></p>
                    <p>下面是一张测试图片:</p>
                    <img src="{data_url}" style="max-width: 200px; max-height: 150px;" />
                    <p><i>图片上方的文字</i></p>
                    <p style="color: blue;">这是蓝色的文字</p>
                    '''
                    return html
            else:
                return '<p><b>测试图片不存在，请先创建测试图片</b></p>'
                
        except Exception as e:
            return f'<p><b>创建测试HTML失败: {str(e)}</b></p>'

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = ImageTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
