# coding:utf-8
from PyQt6.QtWidgets import QLabel, QTextEdit
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QTextDocument, QTextOption
import re

class RichTextDisplay(QLabel):
    """富文本显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWordWrap(True)
        self.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        
    def setRichText(self, text: str, max_length: int = 40):
        """设置富文本内容"""
        if not text:
            self.setText("")
            self.setToolTip("")
            return

        # 检查是否为HTML格式
        if self._is_html(text):
            # 处理HTML内容，将图片替换为[图片]标记
            processed_text = self._replace_images_with_placeholder(text)
            plain_text = self._html_to_plain_text(processed_text)
            display_text, tooltip = self._get_display_text_and_tooltip(plain_text, max_length)

            # 显示处理后的文本
            self.setText(display_text)
            # 工具提示显示原始内容（包含图片）
            self.setToolTip(self._create_rich_tooltip(text))
        else:
            # 处理纯文本
            display_text, tooltip = self._get_display_text_and_tooltip(text, max_length)
            self.setText(display_text)
            self.setToolTip(tooltip)
            
    def _is_html(self, text: str) -> bool:
        """检查文本是否为HTML格式"""
        # 简单检查是否包含HTML标签
        html_pattern = r'<[^>]+>'
        return bool(re.search(html_pattern, text))
        
    def _html_to_plain_text(self, html: str) -> str:
        """将HTML转换为纯文本"""
        # 使用QTextDocument来提取纯文本
        doc = QTextDocument()
        doc.setHtml(html)
        return doc.toPlainText()
        
    def _get_display_text_and_tooltip(self, text: str, max_length: int):
        """获取显示文本和工具提示"""
        if len(text) <= max_length:
            return text, ""
        else:
            display_text = text[:max_length] + "..."
            return display_text, text
            
    def _create_rich_tooltip(self, html: str) -> str:
        """创建富文本工具提示"""
        # 简化HTML以适合工具提示显示
        # 移除一些复杂的样式，保留基本格式
        simplified_html = html

        # 移除字体大小和颜色等样式，保留基本格式
        simplified_html = re.sub(r'font-size:[^;]*;?', '', simplified_html)
        simplified_html = re.sub(r'color:[^;]*;?', '', simplified_html)
        simplified_html = re.sub(r'background-color:[^;]*;?', '', simplified_html)

        return simplified_html

    def _replace_images_with_placeholder(self, html: str) -> str:
        """将HTML中的图片替换为[图片]占位符"""
        # 使用正则表达式查找并替换img标签
        img_pattern = r'<img[^>]*>'
        return re.sub(img_pattern, '[图片]', html)


class CompactRichTextDisplay(QTextEdit):
    """紧凑的富文本显示组件，用于在列表项中显示"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setFrameStyle(0)  # 无边框
        self.setStyleSheet("background: transparent; border: none;")

        # 设置文档选项
        self.document().setDocumentMargin(0)

        # 设置图片最大宽度
        self.document().setTextWidth(300)  # 限制文档宽度以控制图片大小
        
    def setRichText(self, text: str, max_height: int = 40, show_images: bool = False):
        """设置富文本内容"""
        if not text:
            self.clear()
            self.setToolTip("")
            return

        # 检查是否为HTML格式
        if self._is_html(text):
            if show_images:
                # 显示完整内容包括图片
                processed_html = self._process_images_in_html(text)
                self.setHtml(processed_html)
            else:
                # 将图片替换为占位符
                processed_text = self._replace_images_with_placeholder(text)
                self.setHtml(processed_text)

            # 设置工具提示为完整内容
            plain_text = self.toPlainText()
            if len(plain_text) > 50:  # 如果内容较长，显示工具提示
                self.setToolTip(plain_text)
            else:
                self.setToolTip("")
        else:
            self.setPlainText(text)
            if len(text) > 50:
                self.setToolTip(text)
            else:
                self.setToolTip("")

        # 调整高度以适应内容
        self._adjust_height(max_height)

    def _replace_images_with_placeholder(self, html: str) -> str:
        """将HTML中的图片替换为[图片]占位符"""
        # 使用正则表达式查找并替换img标签
        img_pattern = r'<img[^>]*>'
        return re.sub(img_pattern, '<span style="color: #666; background: #f0f0f0; padding: 2px 6px; border-radius: 3px;">[图片]</span>', html)
        
    def _is_html(self, text: str) -> bool:
        """检查文本是否为HTML格式"""
        html_pattern = r'<[^>]+>'
        return bool(re.search(html_pattern, text))
        
    def _adjust_height(self, max_height: int):
        """调整高度以适应内容"""
        # 计算内容高度
        doc = self.document()
        doc_height = doc.size().height()
        
        # 设置合适的高度，但不超过最大高度
        height = min(int(doc_height) + 5, max_height)
        self.setFixedHeight(height)
        
    def resizeEvent(self, event):
        """重写resize事件以保持高度调整"""
        super().resizeEvent(event)
        # 重新调整高度
        if hasattr(self, '_max_height'):
            self._adjust_height(self._max_height)

    def _process_images_in_html(self, html: str) -> str:
        """处理HTML中的图片，调整其大小以适应显示"""
        import re

        # 查找所有img标签
        img_pattern = r'<img([^>]*?)>'

        def replace_img(match):
            img_attrs = match.group(1)
            # 添加样式来限制图片大小
            style = 'style="max-width: 200px; max-height: 150px; object-fit: contain;"'

            # 如果已经有style属性，则合并
            if 'style=' in img_attrs:
                # 简单的样式合并
                img_attrs = re.sub(r'style="([^"]*)"',
                                 lambda m: f'style="{m.group(1)}; max-width: 200px; max-height: 150px; object-fit: contain;"',
                                 img_attrs)
            else:
                img_attrs += f' {style}'

            return f'<img{img_attrs}>'

        return re.sub(img_pattern, replace_img, html)
