# coding:utf-8
from PyQt6.QtWidgets import Q<PERSON>abel, QTextEdit
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QTextDocument, QTextOption
import re

class RichTextDisplay(QLabel):
    """富文本显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWordWrap(True)
        self.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        
    def setRichText(self, text: str, max_length: int = 40):
        """设置富文本内容"""
        if not text:
            self.setText("")
            self.setToolTip("")
            return
            
        # 检查是否为HTML格式
        if self._is_html(text):
            # 处理HTML内容
            plain_text = self._html_to_plain_text(text)
            display_text, tooltip = self._get_display_text_and_tooltip(plain_text, max_length)
            
            # 如果需要截断，则显示纯文本，否则显示富文本
            if len(plain_text) > max_length:
                self.setText(display_text)
                self.setToolTip(self._create_rich_tooltip(text))
            else:
                self.setText(text)
                self.setToolTip("")
        else:
            # 处理纯文本
            display_text, tooltip = self._get_display_text_and_tooltip(text, max_length)
            self.setText(display_text)
            self.setToolTip(tooltip)
            
    def _is_html(self, text: str) -> bool:
        """检查文本是否为HTML格式"""
        # 简单检查是否包含HTML标签
        html_pattern = r'<[^>]+>'
        return bool(re.search(html_pattern, text))
        
    def _html_to_plain_text(self, html: str) -> str:
        """将HTML转换为纯文本"""
        # 使用QTextDocument来提取纯文本
        doc = QTextDocument()
        doc.setHtml(html)
        return doc.toPlainText()
        
    def _get_display_text_and_tooltip(self, text: str, max_length: int):
        """获取显示文本和工具提示"""
        if len(text) <= max_length:
            return text, ""
        else:
            display_text = text[:max_length] + "..."
            return display_text, text
            
    def _create_rich_tooltip(self, html: str) -> str:
        """创建富文本工具提示"""
        # 简化HTML以适合工具提示显示
        # 移除一些复杂的样式，保留基本格式
        simplified_html = html
        
        # 移除字体大小和颜色等样式，保留基本格式
        simplified_html = re.sub(r'font-size:[^;]*;?', '', simplified_html)
        simplified_html = re.sub(r'color:[^;]*;?', '', simplified_html)
        simplified_html = re.sub(r'background-color:[^;]*;?', '', simplified_html)
        
        return simplified_html


class CompactRichTextDisplay(QTextEdit):
    """紧凑的富文本显示组件，用于在列表项中显示"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setFrameStyle(0)  # 无边框
        self.setStyleSheet("background: transparent; border: none;")
        
        # 设置文档选项
        self.document().setDocumentMargin(0)
        
    def setRichText(self, text: str, max_height: int = 40):
        """设置富文本内容"""
        if not text:
            self.clear()
            self.setToolTip("")
            return
            
        # 检查是否为HTML格式
        if self._is_html(text):
            self.setHtml(text)
            # 设置工具提示为完整内容
            plain_text = self.toPlainText()
            if len(plain_text) > 50:  # 如果内容较长，显示工具提示
                self.setToolTip(plain_text)
            else:
                self.setToolTip("")
        else:
            self.setPlainText(text)
            if len(text) > 50:
                self.setToolTip(text)
            else:
                self.setToolTip("")
                
        # 调整高度以适应内容
        self._adjust_height(max_height)
        
    def _is_html(self, text: str) -> bool:
        """检查文本是否为HTML格式"""
        html_pattern = r'<[^>]+>'
        return bool(re.search(html_pattern, text))
        
    def _adjust_height(self, max_height: int):
        """调整高度以适应内容"""
        # 计算内容高度
        doc = self.document()
        doc_height = doc.size().height()
        
        # 设置合适的高度，但不超过最大高度
        height = min(int(doc_height) + 5, max_height)
        self.setFixedHeight(height)
        
    def resizeEvent(self, event):
        """重写resize事件以保持高度调整"""
        super().resizeEvent(event)
        # 重新调整高度
        if hasattr(self, '_max_height'):
            self._adjust_height(self._max_height)
