# coding:utf-8
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QStackedWidget
from PyQt6.QtCore import Qt, pyqtSignal
from qfluentwidgets import (PushButton, TitleLabel, FluentIcon as FIF)
from models import Project
from todo_panel import TodoPanel
from issue_panel import IssuePanel
from detail_view import TodoDetailView, IssueDetailView
from text_utils import get_display_text_and_tooltip

class ProjectDetailWidget(QWidget):
    """项目详情页面"""
    back_requested = pyqtSignal()  # 返回信号
    project_updated = pyqtSignal(Project)  # 项目更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_project: Project = None
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 20)  # 减少顶部边距
        layout.setSpacing(10)  # 设置组件间距

        # 顶部导航栏
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)  # 移除header内边距
        header_layout.setSpacing(15)  # 设置header内组件间距

        # 返回按钮
        self.back_button = PushButton(FIF.LEFT_ARROW, "返回")
        self.back_button.clicked.connect(self.back_requested.emit)
        self.back_button.setFixedHeight(32)  # 固定按钮高度

        # 项目标题
        self.project_title = TitleLabel("项目详情")

        header_layout.addWidget(self.back_button)
        header_layout.addWidget(self.project_title)
        header_layout.addStretch()

        # 分割器 - 左侧TODO，右侧Issue
        self.splitter = QSplitter(Qt.Orientation.Horizontal)

        # TODO面板
        self.todo_panel = TodoPanel()

        # Issue面板
        self.issue_panel = IssuePanel()

        # 详情页面
        self.todo_detail_view = TodoDetailView()
        self.issue_detail_view = IssueDetailView()

        self.splitter.addWidget(self.todo_panel)
        self.splitter.addWidget(self.issue_panel)
        self.splitter.setSizes([400, 400])  # 设置初始大小

        # 创建堆叠窗口来管理列表和详情页面
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.addWidget(self.splitter)  # 0: 列表页面
        self.stacked_widget.addWidget(self.todo_detail_view)  # 1: TODO详情页面
        self.stacked_widget.addWidget(self.issue_detail_view)  # 2: Issue详情页面

        layout.addLayout(header_layout)
        layout.addWidget(self.stacked_widget, 1)  # 给stacked_widget设置拉伸因子
        
    def set_project(self, project: Project):
        """设置当前项目"""
        self.current_project = project

        # 设置项目标题，如果过长则截断并添加提示
        title_text = f"{project.name} - 项目详情"
        title_display, title_tooltip = get_display_text_and_tooltip(title_text, 40)
        self.project_title.setText(title_display)
        if title_tooltip:
            self.project_title.setToolTip(f"项目: {project.name}")

        # 设置TODO和Issue数据
        self.todo_panel.set_todos(project.todos)
        self.issue_panel.set_issues(project.issues)
        
    # TODO相关方法
    def add_todo_item(self, title: str, description: str):
        """添加TODO项"""
        if self.current_project:
            todo = self.current_project.add_todo(title, description)
            self.todo_panel.set_todos(self.current_project.todos)
            self.project_updated.emit(self.current_project)
            
    def update_todo_item(self, todo_id: str, title: str, description: str):
        """更新TODO项"""
        if self.current_project:
            for todo in self.current_project.todos:
                if todo.id == todo_id:
                    todo.title = title
                    todo.description = description
                    break
            self.todo_panel.set_todos(self.current_project.todos)
            self.project_updated.emit(self.current_project)
            
    def delete_todo_item(self, todo_id: str):
        """删除TODO项"""
        if self.current_project:
            self.current_project.remove_todo(todo_id)
            self.todo_panel.set_todos(self.current_project.todos)
            self.project_updated.emit(self.current_project)
            
    def update_todo_completed(self, todo_id: str, completed: bool):
        """更新TODO完成状态"""
        if self.current_project:
            for todo in self.current_project.todos:
                if todo.id == todo_id:
                    todo.completed = completed
                    break
            self.project_updated.emit(self.current_project)
            
    def reorder_todo_items(self, todo_ids: list):
        """重新排序TODO项"""
        if self.current_project:
            self.current_project.reorder_todos(todo_ids)
            self.project_updated.emit(self.current_project)
            
    # Issue相关方法
    def add_issue_item(self, title: str, description: str):
        """添加Issue项"""
        if self.current_project:
            issue = self.current_project.add_issue(title, description)
            self.issue_panel.set_issues(self.current_project.issues)
            self.project_updated.emit(self.current_project)
            
    def update_issue_item(self, issue_id: str, title: str, description: str):
        """更新Issue项"""
        if self.current_project:
            for issue in self.current_project.issues:
                if issue.id == issue_id:
                    issue.title = title
                    issue.description = description
                    break
            self.issue_panel.set_issues(self.current_project.issues)
            self.project_updated.emit(self.current_project)
            
    def delete_issue_item(self, issue_id: str):
        """删除Issue项"""
        if self.current_project:
            self.current_project.remove_issue(issue_id)
            self.issue_panel.set_issues(self.current_project.issues)
            self.project_updated.emit(self.current_project)
            
    def reorder_issue_items(self, issue_ids: list):
        """重新排序Issue项"""
        if self.current_project:
            self.current_project.reorder_issues(issue_ids)
            self.project_updated.emit(self.current_project)

    def setup_connections(self):
        """设置信号连接"""
        # TODO详情查看
        self.todo_panel.todo_detail_requested.connect(self.show_todo_detail)
        self.todo_detail_view.back_requested.connect(self.show_list_view)
        self.todo_detail_view.edit_requested.connect(self.edit_todo_from_detail)
        self.todo_detail_view.delete_requested.connect(self.delete_todo_from_detail)

        # Issue详情查看
        self.issue_panel.issue_detail_requested.connect(self.show_issue_detail)
        self.issue_detail_view.back_requested.connect(self.show_list_view)
        self.issue_detail_view.edit_requested.connect(self.edit_issue_from_detail)
        self.issue_detail_view.delete_requested.connect(self.delete_issue_from_detail)

    def show_todo_detail(self, todo_id: str):
        """显示TODO详情"""
        if self.current_project:
            todo = next((t for t in self.current_project.todos if t.id == todo_id), None)
            if todo:
                self.todo_detail_view.set_item(todo)
                self.stacked_widget.setCurrentIndex(1)  # 切换到TODO详情页面

    def show_issue_detail(self, issue_id: str):
        """显示Issue详情"""
        if self.current_project:
            issue = next((i for i in self.current_project.issues if i.id == issue_id), None)
            if issue:
                self.issue_detail_view.set_item(issue)
                self.stacked_widget.setCurrentIndex(2)  # 切换到Issue详情页面

    def show_list_view(self):
        """显示列表页面"""
        self.stacked_widget.setCurrentIndex(0)  # 切换回列表页面

    def edit_todo_from_detail(self, todo_id: str):
        """从详情页面编辑TODO"""
        self.show_list_view()  # 先返回列表页面
        self.todo_panel.edit_todo(todo_id)  # 然后编辑

    def edit_issue_from_detail(self, issue_id: str):
        """从详情页面编辑Issue"""
        self.show_list_view()  # 先返回列表页面
        self.issue_panel.edit_issue(issue_id)  # 然后编辑

    def delete_todo_from_detail(self, todo_id: str):
        """从详情页面删除TODO"""
        self.show_list_view()  # 先返回列表页面
        self.todo_panel.delete_todo(todo_id)  # 然后删除

    def delete_issue_from_detail(self, issue_id: str):
        """从详情页面删除Issue"""
        self.show_list_view()  # 先返回列表页面
        self.issue_panel.delete_issue(issue_id)  # 然后删除
