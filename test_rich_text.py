# coding:utf-8
"""
富文本功能测试
验证富文本编辑器、显示组件和数据保存功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt6.QtCore import Qt
from rich_text_editor import RichTextEditor
from rich_text_display import RichTextDisplay, CompactRichTextDisplay
from models import Project, TodoItem, IssueItem
from data_manager import DataManager

class RichTextTestWindow(QMainWindow):
    """富文本测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("富文本功能测试")
        self.setGeometry(100, 100, 800, 600)
        self.setup_ui()
        self.setup_test_data()
        
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 富文本编辑器测试
        layout.addWidget(QPushButton("富文本编辑器测试", clicked=self.test_rich_text_editor))
        
        # 富文本显示测试
        layout.addWidget(QPushButton("富文本显示测试", clicked=self.test_rich_text_display))
        
        # 数据保存和加载测试
        layout.addWidget(QPushButton("数据保存加载测试", clicked=self.test_data_persistence))
        
        # TODO和Issue集成测试
        layout.addWidget(QPushButton("TODO/Issue集成测试", clicked=self.test_integration))
        
        # 测试结果显示区域
        self.result_display = RichTextDisplay()
        layout.addWidget(self.result_display)
        
    def setup_test_data(self):
        """设置测试数据"""
        self.test_project = Project(name="富文本测试项目", description="用于测试富文本功能")
        
        # 添加包含富文本的TODO项
        html_todo = self.test_project.add_todo(
            "富文本TODO测试",
            '<p><b>这是粗体文本</b></p><p><i>这是斜体文本</i></p><p><u>这是下划线文本</u></p><p style="color: red;">这是红色文本</p>'
        )
        
        # 添加纯文本TODO项
        plain_todo = self.test_project.add_todo(
            "纯文本TODO测试",
            "这是一个普通的纯文本描述，没有任何格式化。"
        )
        
        # 添加包含富文本的Issue项
        html_issue = self.test_project.add_issue(
            "富文本Issue测试",
            '<p><b>Bug描述：</b></p><ul><li>问题1：<span style="color: red;">严重错误</span></li><li>问题2：<span style="color: orange;">警告</span></li></ul>'
        )
        
        # 添加纯文本Issue项
        plain_issue = self.test_project.add_issue(
            "纯文本Issue测试",
            "这是一个普通的Issue描述，用于测试纯文本显示。"
        )
        
    def test_rich_text_editor(self):
        """测试富文本编辑器"""
        print("🎯 测试富文本编辑器")
        
        # 创建编辑器窗口
        editor_window = QWidget()
        editor_window.setWindowTitle("富文本编辑器测试")
        editor_window.setGeometry(200, 200, 600, 400)
        
        layout = QVBoxLayout(editor_window)
        
        # 富文本编辑器
        editor = RichTextEditor()
        editor.setPlaceholderText("请在此输入富文本内容进行测试...")
        layout.addWidget(editor)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        # 设置测试内容
        def set_test_content():
            test_html = '''
            <p><b>粗体文本</b> 和 <i>斜体文本</i></p>
            <p><u>下划线文本</u> 和 <span style="color: red;">红色文本</span></p>
            <p style="text-align: center;">居中对齐的文本</p>
            <ul>
                <li>列表项 1</li>
                <li>列表项 2</li>
            </ul>
            '''
            editor.setHtml(test_html)
            
        # 获取HTML内容
        def get_html():
            html = editor.toHtml()
            print("HTML内容：", html[:200] + "..." if len(html) > 200 else html)
            
        # 获取纯文本内容
        def get_plain():
            plain = editor.toPlainText()
            print("纯文本内容：", plain)
            
        button_layout.addWidget(QPushButton("设置测试内容", clicked=set_test_content))
        button_layout.addWidget(QPushButton("获取HTML", clicked=get_html))
        button_layout.addWidget(QPushButton("获取纯文本", clicked=get_plain))
        
        layout.addLayout(button_layout)
        
        editor_window.show()
        self.editor_window = editor_window  # 保持引用
        
        self.result_display.setRichText("✅ 富文本编辑器测试窗口已打开")
        
    def test_rich_text_display(self):
        """测试富文本显示"""
        print("🎯 测试富文本显示")
        
        # 创建显示测试窗口
        display_window = QWidget()
        display_window.setWindowTitle("富文本显示测试")
        display_window.setGeometry(300, 300, 500, 400)
        
        layout = QVBoxLayout(display_window)
        
        # 测试不同类型的内容
        test_contents = [
            ("HTML内容", '<p><b>粗体</b> <i>斜体</i> <u>下划线</u></p><p style="color: blue;">蓝色文本</p>'),
            ("纯文本内容", "这是一个普通的纯文本内容，没有任何格式化。"),
            ("长文本内容", "这是一个很长的文本内容，用于测试截断和工具提示功能。" * 5),
            ("混合内容", '<p>这是一个<b>混合内容</b>的例子，包含<i>格式化文本</i>和<span style="color: red;">彩色文本</span>。</p>')
        ]
        
        for title, content in test_contents:
            layout.addWidget(QPushButton(title))
            
            # RichTextDisplay测试
            display = RichTextDisplay()
            display.setRichText(content, 30)
            layout.addWidget(display)
            
            # CompactRichTextDisplay测试
            compact_display = CompactRichTextDisplay()
            compact_display.setRichText(content, 60)
            layout.addWidget(compact_display)
            
            layout.addWidget(QPushButton("---"))
            
        display_window.show()
        self.display_window = display_window  # 保持引用
        
        self.result_display.setRichText("✅ 富文本显示测试窗口已打开")
        
    def test_data_persistence(self):
        """测试数据保存和加载"""
        print("🎯 测试数据保存和加载")
        
        try:
            # 创建数据管理器
            data_manager = DataManager("test_rich_text_data.json")
            
            # 保存测试项目
            projects = [self.test_project]
            data_manager.save_projects(projects)
            print("✅ 数据保存成功")
            
            # 加载数据
            loaded_projects = data_manager.load_projects()
            print(f"✅ 数据加载成功，共加载 {len(loaded_projects)} 个项目")
            
            if loaded_projects:
                project = loaded_projects[0]
                print(f"项目名称: {project.name}")
                print(f"TODO数量: {len(project.todos)}")
                print(f"Issue数量: {len(project.issues)}")
                
                # 检查富文本内容是否正确保存和加载
                for todo in project.todos:
                    print(f"TODO: {todo.title}")
                    print(f"描述: {todo.description[:100]}...")
                    
                for issue in project.issues:
                    print(f"Issue: {issue.title}")
                    print(f"描述: {issue.description[:100]}...")
                    
            self.result_display.setRichText("✅ 数据保存和加载测试完成，检查控制台输出")
            
        except Exception as e:
            error_msg = f"❌ 数据保存加载测试失败: {str(e)}"
            print(error_msg)
            self.result_display.setRichText(error_msg)
            
    def test_integration(self):
        """测试TODO和Issue集成"""
        print("🎯 测试TODO和Issue集成")
        
        try:
            from todo_panel import TodoEditDialog, TodoItemWidget
            from issue_panel import IssueEditDialog, IssueItemWidget
            
            # 测试TODO编辑对话框
            todo_dialog = TodoEditDialog(self.test_project.todos[0])
            print("✅ TODO编辑对话框创建成功")
            
            # 测试Issue编辑对话框
            issue_dialog = IssueEditDialog(self.test_project.issues[0])
            print("✅ Issue编辑对话框创建成功")
            
            # 测试TODO项组件
            todo_widget = TodoItemWidget(self.test_project.todos[0])
            print("✅ TODO项组件创建成功")
            
            # 测试Issue项组件
            issue_widget = IssueItemWidget(self.test_project.issues[0])
            print("✅ Issue项组件创建成功")
            
            self.result_display.setRichText("✅ TODO和Issue集成测试完成，所有组件创建成功")
            
        except Exception as e:
            error_msg = f"❌ 集成测试失败: {str(e)}"
            print(error_msg)
            self.result_display.setRichText(error_msg)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = RichTextTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
