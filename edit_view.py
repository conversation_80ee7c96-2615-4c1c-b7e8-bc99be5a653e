# coding:utf-8
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QScrollArea, QPushButton, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from qfluentwidgets import (CardWidget, SubtitleLabel, BodyLabel, PrimaryPushButton,
                           PushButton, FluentIcon as FIF, ToolButton, LineEdit)
from rich_text_editor import RichTextEditor
from models import TodoItem, IssueItem

class EditView(QWidget):
    """编辑页面基类"""
    
    back_requested = pyqtSignal()  # 返回信号
    save_requested = pyqtSignal(str, str, str)  # 保存信号，传递item_id, title, description
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_item = None
        self.is_new_item = False
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        
        # 返回按钮
        self.back_btn = ToolButton(FIF.LEFT_ARROW)
        self.back_btn.setToolTip("返回")
        self.back_btn.clicked.connect(self._on_back_clicked)
        toolbar_layout.addWidget(self.back_btn)
        
        # 标题区域
        self.page_title = SubtitleLabel("编辑")
        toolbar_layout.addWidget(self.page_title)
        
        # 弹性空间
        toolbar_layout.addStretch()
        
        # 保存按钮
        self.save_btn = PrimaryPushButton("保存")
        self.save_btn.setIcon(FIF.SAVE)
        self.save_btn.clicked.connect(self._on_save_clicked)
        toolbar_layout.addWidget(self.save_btn)
        
        # 取消按钮
        self.cancel_btn = PushButton("取消")
        self.cancel_btn.clicked.connect(self._on_back_clicked)
        toolbar_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
        
        # 编辑区域
        edit_card = CardWidget()
        edit_layout = QVBoxLayout(edit_card)
        edit_layout.setContentsMargins(20, 20, 20, 20)
        edit_layout.setSpacing(15)
        
        # 标题输入
        title_label = BodyLabel("标题")
        title_label.setStyleSheet("font-weight: bold; margin: 0px; padding: 0px;")
        edit_layout.addWidget(title_label)
        
        self.title_edit = LineEdit()
        self.title_edit.setPlaceholderText("请输入标题")
        edit_layout.addWidget(self.title_edit)
        
        # 描述输入
        desc_label = BodyLabel("描述")
        desc_label.setStyleSheet("font-weight: bold; margin: 0px; padding: 0px;")
        edit_layout.addWidget(desc_label)
        
        self.desc_edit = RichTextEditor()
        self.desc_edit.setPlaceholderText("请输入描述（可选）")
        # 设置更大的高度以便于编辑
        self.desc_edit.setMinimumHeight(400)
        edit_layout.addWidget(self.desc_edit)
        
        layout.addWidget(edit_card)
        
    def _on_back_clicked(self):
        """返回按钮点击"""
        self.back_requested.emit()
        
    def _on_save_clicked(self):
        """保存按钮点击"""
        title = self.title_edit.text().strip()
        if not title:
            from qfluentwidgets import MessageBox
            MessageBox("错误", "标题不能为空", self).exec()
            return
            
        # 获取富文本内容
        html_content = self.desc_edit.toHtml()
        plain_content = self.desc_edit.toPlainText().strip()
        
        # 检查是否有富文本格式
        if self._has_rich_formatting(html_content):
            description = html_content
        else:
            description = plain_content
            
        # 发送保存信号
        item_id = self.current_item.id if self.current_item else ""
        self.save_requested.emit(item_id, title, description)
        
    def _has_rich_formatting(self, html: str) -> bool:
        """检查HTML内容是否包含富文本格式"""
        formatting_tags = ['<b>', '<i>', '<u>', '<font', '<span', '<p style=', '<img']
        return any(tag in html for tag in formatting_tags)
        
    def set_item(self, item, is_new: bool = False):
        """设置要编辑的项目"""
        self.current_item = item
        self.is_new_item = is_new
        
        if is_new:
            self.page_title.setText(f"添加{self._get_type_text()}")
            self.title_edit.clear()
            self.desc_edit.clear()
        else:
            self.page_title.setText(f"编辑{self._get_type_text()}")
            self.title_edit.setText(item.title)
            
            # 设置描述内容
            if item.description:
                # 检查是否为HTML格式
                if (item.description.strip().startswith('<') and 
                    item.description.strip().endswith('>')):
                    self.desc_edit.setHtml(item.description)
                else:
                    self.desc_edit.setPlainText(item.description)
            else:
                self.desc_edit.clear()
                
    def _get_type_text(self) -> str:
        """获取类型文本"""
        return "项目"

class TodoEditView(EditView):
    """TODO编辑页面"""
    
    def _get_type_text(self) -> str:
        return "TODO"

class IssueEditView(EditView):
    """Issue编辑页面"""
    
    def _get_type_text(self) -> str:
        return "Issue"
