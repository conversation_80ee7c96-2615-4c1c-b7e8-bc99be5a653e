# coding:utf-8
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QToolBar,
                            QTextEdit, QFontComboBox, QSpinBox, QColorDialog,
                            QSizePolicy, QFileDialog, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal, QUrl
from PyQt6.QtGui import (QFont, QTextCharFormat, QColor, QIcon, QPixmap,
                        QPainter, QTextCursor, QAction, QActionGroup, QTextImageFormat)
from qfluentwidgets import (ToolButton, FluentIcon as FIF, ComboBox, SpinBox,
                           PushButton, isDarkTheme)
import base64
import os

class RichTextEditor(QWidget):
    """富文本编辑器组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 工具栏
        self.toolbar = self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # 文本编辑器
        self.text_edit = QTextEdit()
        self.text_edit.setAcceptRichText(True)
        layout.addWidget(self.text_edit)
        
    def create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(4, 4, 4, 4)
        toolbar_layout.setSpacing(4)
        
        # 字体相关
        self.font_combo = QFontComboBox()
        self.font_combo.setMaximumWidth(150)
        self.font_combo.setCurrentFont(QFont("Microsoft YaHei", 9))
        toolbar_layout.addWidget(self.font_combo)
        
        # 字体大小
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 72)
        self.font_size_spin.setValue(9)
        self.font_size_spin.setMaximumWidth(60)
        toolbar_layout.addWidget(self.font_size_spin)
        
        # 分隔符
        toolbar_layout.addWidget(self.create_separator())
        
        # 格式化按钮
        self.bold_btn = ToolButton(FIF.FONT)
        self.bold_btn.setCheckable(True)
        self.bold_btn.setToolTip("粗体 (Ctrl+B)")
        toolbar_layout.addWidget(self.bold_btn)
        
        self.italic_btn = ToolButton(FIF.FONT)
        self.italic_btn.setCheckable(True)
        self.italic_btn.setToolTip("斜体 (Ctrl+I)")
        toolbar_layout.addWidget(self.italic_btn)
        
        self.underline_btn = ToolButton(FIF.FONT)
        self.underline_btn.setCheckable(True)
        self.underline_btn.setToolTip("下划线 (Ctrl+U)")
        toolbar_layout.addWidget(self.underline_btn)
        
        # 分隔符
        toolbar_layout.addWidget(self.create_separator())
        
        # 文字颜色
        self.text_color_btn = PushButton("A")
        self.text_color_btn.setMaximumWidth(30)
        self.text_color_btn.setToolTip("文字颜色")
        toolbar_layout.addWidget(self.text_color_btn)
        
        # 背景颜色
        self.bg_color_btn = PushButton("■")
        self.bg_color_btn.setMaximumWidth(30)
        self.bg_color_btn.setToolTip("背景颜色")
        toolbar_layout.addWidget(self.bg_color_btn)
        
        # 分隔符
        toolbar_layout.addWidget(self.create_separator())

        # 图片插入
        self.image_btn = ToolButton(FIF.PHOTO)
        self.image_btn.setToolTip("插入图片")
        toolbar_layout.addWidget(self.image_btn)

        # 分隔符
        toolbar_layout.addWidget(self.create_separator())

        # 对齐方式
        self.align_left_btn = ToolButton(FIF.ALIGNMENT)
        self.align_left_btn.setCheckable(True)
        self.align_left_btn.setToolTip("左对齐")
        toolbar_layout.addWidget(self.align_left_btn)

        self.align_center_btn = ToolButton(FIF.ALIGNMENT)
        self.align_center_btn.setCheckable(True)
        self.align_center_btn.setToolTip("居中对齐")
        toolbar_layout.addWidget(self.align_center_btn)

        self.align_right_btn = ToolButton(FIF.ALIGNMENT)
        self.align_right_btn.setCheckable(True)
        self.align_right_btn.setToolTip("右对齐")
        toolbar_layout.addWidget(self.align_right_btn)
        
        # 对齐按钮组
        self.align_group = QActionGroup(self)
        
        # 弹性空间
        toolbar_layout.addStretch()
        
        return toolbar_widget
        
    def create_separator(self) -> QWidget:
        """创建分隔符"""
        separator = QWidget()
        separator.setFixedWidth(1)
        separator.setStyleSheet("background-color: #E0E0E0;")
        return separator
        
    def setup_connections(self):
        """设置信号连接"""
        # 字体和大小
        self.font_combo.currentFontChanged.connect(self.change_font)
        self.font_size_spin.valueChanged.connect(self.change_font_size)
        
        # 格式化
        self.bold_btn.clicked.connect(self.toggle_bold)
        self.italic_btn.clicked.connect(self.toggle_italic)
        self.underline_btn.clicked.connect(self.toggle_underline)
        
        # 颜色
        self.text_color_btn.clicked.connect(self.change_text_color)
        self.bg_color_btn.clicked.connect(self.change_background_color)

        # 图片
        self.image_btn.clicked.connect(self.insert_image)

        # 对齐
        self.align_left_btn.clicked.connect(lambda: self.set_alignment(Qt.AlignmentFlag.AlignLeft))
        self.align_center_btn.clicked.connect(lambda: self.set_alignment(Qt.AlignmentFlag.AlignCenter))
        self.align_right_btn.clicked.connect(lambda: self.set_alignment(Qt.AlignmentFlag.AlignRight))
        
        # 光标位置变化时更新工具栏状态
        self.text_edit.cursorPositionChanged.connect(self.update_toolbar_state)
        
    def change_font(self, font: QFont):
        """改变字体"""
        cursor = self.text_edit.textCursor()
        if cursor.hasSelection():
            char_format = QTextCharFormat()
            char_format.setFontFamily(font.family())
            cursor.mergeCharFormat(char_format)
        else:
            self.text_edit.setCurrentFont(font)
            
    def change_font_size(self, size: int):
        """改变字体大小"""
        cursor = self.text_edit.textCursor()
        if cursor.hasSelection():
            char_format = QTextCharFormat()
            char_format.setFontPointSize(size)
            cursor.mergeCharFormat(char_format)
        else:
            font = self.text_edit.currentFont()
            font.setPointSize(size)
            self.text_edit.setCurrentFont(font)
            
    def toggle_bold(self):
        """切换粗体"""
        cursor = self.text_edit.textCursor()
        char_format = QTextCharFormat()
        
        if self.bold_btn.isChecked():
            char_format.setFontWeight(QFont.Weight.Bold)
        else:
            char_format.setFontWeight(QFont.Weight.Normal)
            
        if cursor.hasSelection():
            cursor.mergeCharFormat(char_format)
        else:
            self.text_edit.mergeCurrentCharFormat(char_format)
            
    def toggle_italic(self):
        """切换斜体"""
        cursor = self.text_edit.textCursor()
        char_format = QTextCharFormat()
        char_format.setFontItalic(self.italic_btn.isChecked())
        
        if cursor.hasSelection():
            cursor.mergeCharFormat(char_format)
        else:
            self.text_edit.mergeCurrentCharFormat(char_format)
            
    def toggle_underline(self):
        """切换下划线"""
        cursor = self.text_edit.textCursor()
        char_format = QTextCharFormat()
        char_format.setFontUnderline(self.underline_btn.isChecked())

        if cursor.hasSelection():
            cursor.mergeCharFormat(char_format)
        else:
            self.text_edit.mergeCurrentCharFormat(char_format)

    def change_text_color(self):
        """改变文字颜色"""
        color = QColorDialog.getColor(Qt.GlobalColor.black, self, "选择文字颜色")
        if color.isValid():
            cursor = self.text_edit.textCursor()
            char_format = QTextCharFormat()
            char_format.setForeground(color)

            if cursor.hasSelection():
                cursor.mergeCharFormat(char_format)
            else:
                self.text_edit.mergeCurrentCharFormat(char_format)

            # 更新按钮颜色
            self.text_color_btn.setStyleSheet(f"color: {color.name()};")

    def change_background_color(self):
        """改变背景颜色"""
        color = QColorDialog.getColor(Qt.GlobalColor.white, self, "选择背景颜色")
        if color.isValid():
            cursor = self.text_edit.textCursor()
            char_format = QTextCharFormat()
            char_format.setBackground(color)

            if cursor.hasSelection():
                cursor.mergeCharFormat(char_format)
            else:
                self.text_edit.mergeCurrentCharFormat(char_format)

            # 更新按钮颜色
            self.bg_color_btn.setStyleSheet(f"background-color: {color.name()};")

    def set_alignment(self, alignment):
        """设置对齐方式"""
        self.text_edit.setAlignment(alignment)

        # 更新按钮状态
        self.align_left_btn.setChecked(alignment == Qt.AlignmentFlag.AlignLeft)
        self.align_center_btn.setChecked(alignment == Qt.AlignmentFlag.AlignCenter)
        self.align_right_btn.setChecked(alignment == Qt.AlignmentFlag.AlignRight)

    def update_toolbar_state(self):
        """更新工具栏状态"""
        cursor = self.text_edit.textCursor()
        char_format = cursor.charFormat()

        # 更新字体
        font = char_format.font()
        self.font_combo.setCurrentFont(font)

        # 更新字体大小
        if char_format.fontPointSize() > 0:
            self.font_size_spin.setValue(int(char_format.fontPointSize()))

        # 更新格式化按钮
        self.bold_btn.setChecked(char_format.fontWeight() == QFont.Weight.Bold)
        self.italic_btn.setChecked(char_format.fontItalic())
        self.underline_btn.setChecked(char_format.fontUnderline())

        # 更新对齐方式
        alignment = self.text_edit.alignment()
        self.align_left_btn.setChecked(alignment == Qt.AlignmentFlag.AlignLeft)
        self.align_center_btn.setChecked(alignment == Qt.AlignmentFlag.AlignCenter)
        self.align_right_btn.setChecked(alignment == Qt.AlignmentFlag.AlignRight)

    def setHtml(self, html: str):
        """设置HTML内容"""
        self.text_edit.setHtml(html)

    def toHtml(self) -> str:
        """获取HTML内容"""
        return self.text_edit.toHtml()

    def setPlainText(self, text: str):
        """设置纯文本内容"""
        self.text_edit.setPlainText(text)

    def toPlainText(self) -> str:
        """获取纯文本内容"""
        return self.text_edit.toPlainText()

    def setPlaceholderText(self, text: str):
        """设置占位符文本"""
        self.text_edit.setPlaceholderText(text)

    def setFixedHeight(self, height: int):
        """设置固定高度"""
        self.text_edit.setFixedHeight(height - 40)  # 减去工具栏高度

    def clear(self):
        """清空内容"""
        self.text_edit.clear()

    def isEmpty(self) -> bool:
        """检查是否为空"""
        return not self.text_edit.toPlainText().strip()

    def insert_image(self):
        """插入图片"""
        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择图片文件",
            "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp *.svg);;所有文件 (*)"
        )

        if file_path:
            try:
                # 检查文件大小（限制为5MB）
                file_size = os.path.getsize(file_path)
                if file_size > 5 * 1024 * 1024:  # 5MB
                    QMessageBox.warning(self, "文件过大", "图片文件大小不能超过5MB")
                    return

                # 读取图片文件并转换为Base64
                with open(file_path, 'rb') as f:
                    image_data = f.read()

                # 获取文件扩展名
                _, ext = os.path.splitext(file_path)
                ext = ext.lower().lstrip('.')

                # 转换为Base64
                base64_data = base64.b64encode(image_data).decode('utf-8')

                # 创建data URL
                mime_type = self._get_mime_type(ext)
                data_url = f"data:{mime_type};base64,{base64_data}"

                # 插入图片到文档
                self._insert_image_to_document(data_url, os.path.basename(file_path))

            except Exception as e:
                QMessageBox.critical(self, "错误", f"插入图片失败：{str(e)}")

    def _get_mime_type(self, ext: str) -> str:
        """根据文件扩展名获取MIME类型"""
        mime_types = {
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'svg': 'image/svg+xml'
        }
        return mime_types.get(ext, 'image/png')

    def _insert_image_to_document(self, data_url: str, filename: str):
        """将图片插入到文档中"""
        cursor = self.text_edit.textCursor()

        # 创建图片格式
        image_format = QTextImageFormat()
        image_format.setName(data_url)

        # 设置图片大小（可选，这里设置最大宽度为400px）
        # 注意：QTextImageFormat的尺寸设置可能不会立即生效
        image_format.setWidth(400)
        image_format.setHeight(300)  # 可以设置为0让其自动调整比例

        # 插入图片
        cursor.insertImage(image_format)

        # 在图片后添加一个换行
        cursor.insertText("\n")

    def _resize_image_data(self, image_data: bytes, max_width: int = 800, max_height: int = 600) -> bytes:
        """调整图片大小以减少文件大小"""
        try:
            from PyQt6.QtGui import QPixmap
            from PyQt6.QtCore import QBuffer, QIODevice

            # 加载图片
            pixmap = QPixmap()
            pixmap.loadFromData(image_data)

            # 如果图片太大，则缩放
            if pixmap.width() > max_width or pixmap.height() > max_height:
                pixmap = pixmap.scaled(
                    max_width, max_height,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

            # 转换回字节数据
            buffer = QBuffer()
            buffer.open(QIODevice.OpenModeFlag.WriteOnly)
            pixmap.save(buffer, "PNG")
            return buffer.data().data()

        except Exception:
            # 如果调整大小失败，返回原始数据
            return image_data
