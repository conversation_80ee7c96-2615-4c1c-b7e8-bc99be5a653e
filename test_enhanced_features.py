# coding:utf-8
"""
增强功能测试
验证图片粘贴、列表显示优化、详情页面导航等新功能
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                            QPushButton, QHBoxLayout, QLabel, QTextEdit)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap, QPainter
from rich_text_editor import RichTextEditor
from rich_text_display import RichTextDisplay, CompactRichTextDisplay
from detail_view import TodoDetailView, IssueDetailView
from models import Project, TodoItem, IssueItem
from data_manager import DataManager
import base64

class EnhancedFeaturesTestWindow(QMainWindow):
    """增强功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("增强功能测试")
        self.setGeometry(100, 100, 1200, 800)
        self.setup_ui()
        self.create_test_data()
        
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮区域
        button_layout = QHBoxLayout()
        
        # 图片粘贴测试
        button_layout.addWidget(QPushButton("测试图片粘贴", clicked=self.test_image_paste))
        
        # 列表显示测试
        button_layout.addWidget(QPushButton("测试列表显示", clicked=self.test_list_display))
        
        # 详情页面测试
        button_layout.addWidget(QPushButton("测试详情页面", clicked=self.test_detail_view))
        
        # 编辑框大小测试
        button_layout.addWidget(QPushButton("测试编辑框大小", clicked=self.test_editor_size))
        
        layout.addLayout(button_layout)
        
        # 结果显示区域
        self.result_display = QTextEdit()
        self.result_display.setMaximumHeight(150)
        layout.addWidget(QLabel("测试结果:"))
        layout.addWidget(self.result_display)
        
        # 预览区域
        self.preview_area = CompactRichTextDisplay()
        layout.addWidget(QLabel("预览区域:"))
        layout.addWidget(self.preview_area)
        
    def create_test_data(self):
        """创建测试数据"""
        self.test_project = Project(name="增强功能测试项目", description="用于测试新增功能")
        
        # 创建包含图片的HTML内容
        test_image_html = self._create_test_html_with_image()
        
        # 添加包含图片的TODO项
        self.test_todo = self.test_project.add_todo(
            "包含图片的TODO测试",
            test_image_html
        )
        
        # 添加包含图片的Issue项
        self.test_issue = self.test_project.add_issue(
            "包含图片的Issue测试",
            test_image_html
        )
        
        # 添加纯文本项目用于对比
        self.test_project.add_todo(
            "纯文本TODO",
            "这是一个普通的纯文本TODO描述，没有任何格式化和图片。"
        )
        
        self.test_project.add_issue(
            "纯文本Issue",
            "这是一个普通的纯文本Issue描述，没有任何格式化和图片。"
        )
        
    def test_image_paste(self):
        """测试图片粘贴功能"""
        try:
            # 创建富文本编辑器测试窗口
            editor_window = QWidget()
            editor_window.setWindowTitle("图片粘贴测试")
            editor_window.setGeometry(200, 200, 800, 600)
            
            layout = QVBoxLayout(editor_window)
            
            # 说明文字
            instruction = QLabel("请尝试以下操作：\n1. 复制一张图片到剪贴板\n2. 在下方编辑器中按Ctrl+V粘贴\n3. 观察图片是否成功插入")
            instruction.setStyleSheet("background: #f0f0f0; padding: 10px; border-radius: 5px;")
            layout.addWidget(instruction)
            
            # 富文本编辑器
            editor = RichTextEditor()
            editor.setPlaceholderText("请在此处粘贴图片（Ctrl+V）...")
            layout.addWidget(editor)
            
            # 测试按钮
            button_layout = QHBoxLayout()
            
            def get_content():
                html = editor.toHtml()
                has_image = '<img' in html
                self.result_display.append(f"✅ 内容获取成功，包含图片: {'是' if has_image else '否'}")
                if has_image:
                    self.preview_area.setRichText(html, show_images=True)
                    
            def clear_content():
                editor.clear()
                
            button_layout.addWidget(QPushButton("获取内容", clicked=get_content))
            button_layout.addWidget(QPushButton("清空", clicked=clear_content))
            
            layout.addLayout(button_layout)
            
            editor_window.show()
            self.editor_window = editor_window  # 保持引用
            
            self.result_display.append("✅ 图片粘贴测试窗口已打开")
            
        except Exception as e:
            self.result_display.append(f"❌ 图片粘贴测试失败: {str(e)}")
            
    def test_list_display(self):
        """测试列表显示功能"""
        try:
            # 创建列表显示测试窗口
            list_window = QWidget()
            list_window.setWindowTitle("列表显示测试")
            list_window.setGeometry(300, 300, 600, 500)
            
            layout = QVBoxLayout(list_window)
            
            # 说明
            layout.addWidget(QLabel("测试列表中图片显示为[图片]标记："))
            
            # 测试包含图片的内容
            test_html = self._create_test_html_with_image()
            
            # RichTextDisplay测试（列表模式）
            layout.addWidget(QLabel("RichTextDisplay（列表模式）:"))
            list_display = RichTextDisplay()
            list_display.setRichText(test_html, 50)
            layout.addWidget(list_display)
            
            # CompactRichTextDisplay测试（列表模式）
            layout.addWidget(QLabel("CompactRichTextDisplay（列表模式）:"))
            compact_list_display = CompactRichTextDisplay()
            compact_list_display.setRichText(test_html, max_height=100, show_images=False)
            layout.addWidget(compact_list_display)
            
            # CompactRichTextDisplay测试（详情模式）
            layout.addWidget(QLabel("CompactRichTextDisplay（详情模式）:"))
            compact_detail_display = CompactRichTextDisplay()
            compact_detail_display.setRichText(test_html, max_height=200, show_images=True)
            layout.addWidget(compact_detail_display)
            
            list_window.show()
            self.list_window = list_window  # 保持引用
            
            self.result_display.append("✅ 列表显示测试窗口已打开")
            
        except Exception as e:
            self.result_display.append(f"❌ 列表显示测试失败: {str(e)}")
            
    def test_detail_view(self):
        """测试详情页面功能"""
        try:
            # 创建TODO详情页面测试
            todo_detail_window = TodoDetailView()
            todo_detail_window.setWindowTitle("TODO详情页面测试")
            todo_detail_window.setGeometry(400, 400, 800, 600)
            todo_detail_window.set_item(self.test_todo)
            todo_detail_window.show()
            
            # 创建Issue详情页面测试
            issue_detail_window = IssueDetailView()
            issue_detail_window.setWindowTitle("Issue详情页面测试")
            issue_detail_window.setGeometry(450, 450, 800, 600)
            issue_detail_window.set_item(self.test_issue)
            issue_detail_window.show()
            
            # 保持引用
            self.todo_detail_window = todo_detail_window
            self.issue_detail_window = issue_detail_window
            
            self.result_display.append("✅ 详情页面测试窗口已打开")
            
        except Exception as e:
            self.result_display.append(f"❌ 详情页面测试失败: {str(e)}")
            
    def test_editor_size(self):
        """测试编辑框大小"""
        try:
            from todo_panel import TodoEditDialog
            from issue_panel import IssueEditDialog
            
            # 测试TODO编辑对话框
            todo_dialog = TodoEditDialog(self.test_todo)
            todo_dialog.setWindowTitle("TODO编辑框大小测试")
            todo_dialog.show()
            
            # 测试Issue编辑对话框
            issue_dialog = IssueEditDialog(self.test_issue)
            issue_dialog.setWindowTitle("Issue编辑框大小测试")
            issue_dialog.show()
            
            # 保持引用
            self.todo_dialog = todo_dialog
            self.issue_dialog = issue_dialog
            
            self.result_display.append("✅ 编辑框大小测试对话框已打开")
            
        except Exception as e:
            self.result_display.append(f"❌ 编辑框大小测试失败: {str(e)}")
            
    def _create_test_html_with_image(self) -> str:
        """创建包含图片的测试HTML"""
        try:
            # 创建一个简单的测试图片
            pixmap = QPixmap(100, 80)
            pixmap.fill(Qt.GlobalColor.lightBlue)
            
            painter = QPainter(pixmap)
            painter.setPen(Qt.GlobalColor.darkBlue)
            painter.drawText(20, 40, "测试")
            painter.drawRect(5, 5, 90, 70)
            painter.end()
            
            # 转换为Base64
            from PyQt6.QtCore import QBuffer, QIODevice
            buffer = QBuffer()
            buffer.open(QIODevice.OpenModeFlag.WriteOnly)
            pixmap.save(buffer, "PNG")
            image_data = buffer.data().data()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            data_url = f"data:image/png;base64,{base64_data}"
            
            html = f'''
            <p><b>这是一个包含图片的测试内容</b></p>
            <p>下面是一张测试图片:</p>
            <img src="{data_url}" style="max-width: 100px; max-height: 80px;" />
            <p><i>图片上方的文字</i></p>
            <p style="color: blue;">这是蓝色的文字</p>
            <p>这是一段较长的文字，用于测试文本截断功能。这段文字应该会被截断，完整内容会显示在工具提示中。</p>
            '''
            return html
            
        except Exception as e:
            return f'<p><b>创建测试HTML失败: {str(e)}</b></p>'

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = EnhancedFeaturesTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
