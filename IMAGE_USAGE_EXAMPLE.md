# 图片功能使用示例

## 快速开始

### 1. 启动应用程序
```bash
python todo_app.py
```

### 2. 创建项目
1. 点击"添加项目"按钮
2. 输入项目名称和描述
3. 点击确定创建项目

### 3. 添加包含图片的TODO
1. 在项目详情页面，点击"添加TODO"
2. 输入TODO标题
3. 在描述区域的富文本编辑器中：
   - 输入一些文字描述
   - 点击工具栏中的图片按钮 📷
   - 选择要插入的图片文件
   - 图片会自动插入到光标位置
4. 点击确定保存

### 4. 查看效果
- 在TODO列表中可以看到包含图片的描述
- 图片会自动调整大小以适应列表显示
- 鼠标悬停可查看完整内容

## 支持的图片格式

- **PNG** - 推荐，支持透明背景
- **JPG/JPEG** - 常用格式，文件较小
- **GIF** - 支持动画（静态显示）
- **BMP** - Windows位图格式
- **SVG** - 矢量图形格式

## 图片大小建议

### 文件大小
- **最大限制**: 5MB
- **推荐大小**: 1MB以下
- **优化建议**: 使用压缩工具减小文件大小

### 显示尺寸
- **编辑器中**: 最大400x300px
- **列表显示**: 最大200x150px
- **自动调整**: 保持原始宽高比

## 使用技巧

### 1. 图片插入位置
- 将光标放在要插入图片的位置
- 点击图片按钮选择文件
- 图片会插入到光标位置

### 2. 文字和图片混排
```
这是一段文字描述...

[图片位置]

这是图片下方的说明文字...
```

### 3. 多张图片
- 可以在同一个描述中插入多张图片
- 每张图片会自动换行显示
- 建议在图片间添加文字说明

### 4. 图片优化
- 插入前可以使用图片编辑软件调整大小
- 推荐使用PNG格式以获得最佳质量
- 避免插入过大的图片影响性能

## 数据存储说明

### Base64编码
- 图片数据使用Base64编码存储
- 直接嵌入到HTML内容中
- 无需额外的图片文件

### 优点
- **便携性**: 数据文件包含所有内容
- **完整性**: 不会丢失图片文件
- **跨平台**: 可在不同系统间移动

### 注意事项
- 文件大小会比原始图片大约33%
- 大量图片会增加数据文件大小
- 建议定期清理不需要的图片

## 故障排除

### 图片无法插入
1. 检查文件格式是否支持
2. 确认文件大小不超过5MB
3. 检查文件是否损坏

### 图片显示异常
1. 重新启动应用程序
2. 检查图片数据是否完整
3. 尝试重新插入图片

### 性能问题
1. 减少单个描述中的图片数量
2. 压缩图片文件大小
3. 定期清理不需要的内容

## 最佳实践

### 1. 图片命名
- 使用有意义的文件名
- 便于识别图片内容

### 2. 内容组织
- 图片和文字合理搭配
- 添加图片说明文字
- 保持内容简洁明了

### 3. 文件管理
- 定期备份项目数据
- 清理不需要的图片
- 控制单个项目的大小

### 4. 团队协作
- 统一图片格式标准
- 控制图片文件大小
- 建立图片使用规范

## 示例场景

### 1. Bug报告
```
发现登录页面显示异常

问题描述：
- 用户名输入框位置错误
- 按钮样式不正确

截图：
[插入问题截图]

期望效果：
[插入正确效果图]
```

### 2. 功能设计
```
新功能界面设计

设计说明：
- 采用卡片式布局
- 使用蓝色主题

设计稿：
[插入设计图片]

交互说明：
- 点击卡片展开详情
- 支持拖拽排序
```

### 3. 进度记录
```
项目进度更新

完成情况：
- ✅ 用户界面设计
- ✅ 后端API开发
- 🔄 前端集成

效果展示：
[插入完成效果图]

下一步计划：
- 完成前端集成
- 进行测试验证
```
