# 富文本功能说明

## 概述

为TODO项目管理器添加了完整的富文本支持，包括编辑、显示和数据持久化功能。

## 新增功能

### 1. 富文本编辑器 (RichTextEditor)

**位置**: `rich_text_editor.py`

**功能特性**:
- 完整的富文本编辑工具栏
- 字体选择和大小调整
- 文本格式化：粗体、斜体、下划线
- 文字颜色和背景颜色设置
- 文本对齐方式：左对齐、居中、右对齐
- 支持HTML格式的输入和输出

**工具栏功能**:
- 字体选择下拉框
- 字体大小调节器 (8-72pt)
- 粗体按钮 (Ctrl+B)
- 斜体按钮 (Ctrl+I)
- 下划线按钮 (Ctrl+U)
- 文字颜色选择器
- 背景颜色选择器
- **图片插入按钮** (支持PNG、JPG、GIF等格式)
- 对齐方式按钮组

### 2. 富文本显示组件 (RichTextDisplay)

**位置**: `rich_text_display.py`

**功能特性**:
- 自动检测HTML格式内容
- 智能文本截断和工具提示
- 支持富文本内容的渲染
- 紧凑显示模式适配列表项

**组件类型**:
- `RichTextDisplay`: 基于QLabel的富文本显示
- `CompactRichTextDisplay`: 基于QTextEdit的紧凑显示

### 3. 更新的编辑对话框

**TODO编辑对话框** (`todo_panel.py`):
- 描述输入框替换为富文本编辑器
- 支持HTML格式的保存和加载
- 自动检测内容格式类型

**Issue编辑对话框** (`issue_panel.py`):
- 描述输入框替换为富文本编辑器
- 支持HTML格式的保存和加载
- 自动检测内容格式类型

### 4. 更新的显示组件

**TodoItemWidget**:
- 描述显示支持富文本渲染
- 自动处理HTML和纯文本内容
- 保持原有的截断和工具提示功能

**IssueItemWidget**:
- 描述显示支持富文本渲染
- 自动处理HTML和纯文本内容
- 保持原有的截断和工具提示功能

## 数据格式兼容性

### 自动格式检测
- 系统自动检测内容是否包含HTML标签
- 有格式化的内容保存为HTML格式
- 纯文本内容保存为普通字符串
- 向后兼容现有的纯文本数据

### 格式化检测逻辑
检测以下HTML标签来判断是否为富文本：
- `<b>` - 粗体
- `<i>` - 斜体
- `<u>` - 下划线
- `<font` - 字体设置
- `<span` - 样式设置
- `<p style=` - 段落样式
- `<img` - 图片标签

## 使用方法

### 1. 创建富文本内容
1. 在TODO或Issue编辑对话框中，使用工具栏进行文本格式化
2. 选择文本后点击格式化按钮应用样式
3. 使用颜色按钮设置文字和背景颜色
4. 使用对齐按钮调整文本对齐方式

### 2. 查看富文本内容
- 在项目详情页面，富文本内容会自动渲染显示
- 长内容会被截断，完整内容显示在工具提示中
- 鼠标悬停可查看完整的格式化内容

### 3. 编辑现有内容
- 编辑现有TODO或Issue时，富文本格式会被保留
- 可以继续添加或修改格式化
- 支持在富文本和纯文本之间切换

## 测试功能

### 运行测试程序
```bash
python test_rich_text.py
```

**测试内容**:
1. **富文本编辑器测试** - 打开编辑器窗口，测试各种格式化功能
2. **富文本显示测试** - 测试不同类型内容的显示效果
3. **数据保存加载测试** - 验证富文本数据的持久化
4. **集成测试** - 测试与TODO/Issue组件的集成

### 运行主应用程序
```bash
python todo_app.py
```

在主应用程序中：
1. 创建新项目
2. 添加TODO或Issue
3. 在描述中使用富文本格式化
4. 保存并查看显示效果

## 技术实现

### 核心技术栈
- **PyQt6**: 基础GUI框架
- **QTextEdit**: 富文本编辑核心组件
- **QFluentWidgets**: UI组件库
- **HTML**: 富文本存储格式

### 关键设计决策
1. **格式检测**: 使用HTML标签检测而非文件扩展名
2. **向后兼容**: 保持对现有纯文本数据的支持
3. **智能保存**: 只有包含格式化的内容才保存为HTML
4. **用户体验**: 提供直观的工具栏和即时预览

## 注意事项

1. **性能**: 富文本渲染比纯文本稍慢，但在正常使用中不明显
2. **存储**: HTML格式的内容占用空间比纯文本大
3. **兼容性**: 确保qfluentwidgets库已正确安装
4. **字体**: 默认使用Microsoft YaHei字体，确保中文显示正常

## 图片支持功能 🆕

### 图片插入
- **支持格式**: PNG、JPG、JPEG、GIF、BMP、SVG
- **文件大小限制**: 最大5MB
- **编码方式**: Base64编码嵌入HTML
- **自动调整**: 插入时自动调整图片大小

### 图片显示
- **列表显示**: 在TODO/Issue列表中自动调整图片大小
- **最大尺寸**: 列表中图片最大200x150px
- **保持比例**: 自动保持图片宽高比
- **工具提示**: 支持在工具提示中显示完整内容

### 图片存储
- **嵌入式存储**: 图片数据直接嵌入HTML中
- **无外部依赖**: 不需要额外的图片文件
- **跨平台兼容**: 数据文件可在不同系统间移动

### 使用方法
1. 在富文本编辑器中点击图片按钮
2. 选择要插入的图片文件
3. 图片会自动插入到光标位置
4. 保存后图片数据会嵌入到项目文件中

## 测试功能

### 运行图片测试程序
```bash
python test_image_support.py
```

**图片测试内容**:
1. **富文本编辑器测试** - 测试图片插入和编辑功能
2. **图片显示测试** - 测试不同显示组件中的图片渲染
3. **数据保存测试** - 验证包含图片的数据持久化
4. **创建测试图片** - 自动生成测试用的图片文件

## 未来扩展

可能的功能扩展：
- 支持图片拖拽插入
- 图片大小和位置调整
- 支持表格编辑
- 支持Markdown格式
- 导出为PDF或其他格式
- 更多文本样式选项
